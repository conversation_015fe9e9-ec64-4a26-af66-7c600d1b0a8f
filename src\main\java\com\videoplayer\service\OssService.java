package com.videoplayer.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.videoplayer.config.OssConfig;
import com.videoplayer.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

/**
 * 阿里云OSS服务类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class OssService {

    private static final Logger logger = LoggerFactory.getLogger(OssService.class);

    @Autowired
    private OSS ossClient;

    @Autowired
    private OssConfig ossConfig;

    /**
     * 上传视频文件到OSS
     * 
     * @param file 视频文件
     * @return 视频URL
     */
    public String uploadVideo(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("VIDEO_FILE_EMPTY", "视频文件不能为空");
        }

        // 验证文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("video/")) {
            throw new BusinessException("INVALID_VIDEO_FORMAT", "请上传有效的视频文件");
        }

        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        String fileName = UUID.randomUUID().toString() + fileExtension;
        String objectKey = ossConfig.getVideoDir() + "/" + fileName;

        try (InputStream inputStream = file.getInputStream()) {
            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(contentType);
            metadata.setCacheControl("max-age=31536000"); // 缓存一年

            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                ossConfig.getBucketName(), objectKey, inputStream, metadata);
            
            ossClient.putObject(putObjectRequest);

            // 返回文件URL
            String videoUrl = ossConfig.getBaseUrl() + "/" + objectKey;
            logger.info("视频上传成功: {}", videoUrl);
            return videoUrl;

        } catch (IOException e) {
            logger.error("视频上传失败: {}", e.getMessage(), e);
            throw new BusinessException("VIDEO_UPLOAD_FAILED", "视频上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传缩略图到OSS
     * 
     * @param file 缩略图文件
     * @return 缩略图URL
     */
    public String uploadThumbnail(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return null; // 缩略图是可选的
        }

        // 验证文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new BusinessException("INVALID_IMAGE_FORMAT", "请上传有效的图片文件");
        }

        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        String fileName = UUID.randomUUID().toString() + fileExtension;
        String objectKey = ossConfig.getThumbnailDir() + "/" + fileName;

        try (InputStream inputStream = file.getInputStream()) {
            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(contentType);
            metadata.setCacheControl("max-age=31536000"); // 缓存一年

            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                ossConfig.getBucketName(), objectKey, inputStream, metadata);
            
            ossClient.putObject(putObjectRequest);

            // 返回文件URL
            String thumbnailUrl = ossConfig.getBaseUrl() + "/" + objectKey;
            logger.info("缩略图上传成功: {}", thumbnailUrl);
            return thumbnailUrl;

        } catch (IOException e) {
            logger.error("缩略图上传失败: {}", e.getMessage(), e);
            throw new BusinessException("THUMBNAIL_UPLOAD_FAILED", "缩略图上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除OSS文件
     * 
     * @param fileUrl 文件URL
     */
    public void deleteFile(String fileUrl) {
        if (fileUrl == null || fileUrl.isEmpty()) {
            return;
        }

        try {
            // 从URL中提取对象键
            String objectKey = extractObjectKey(fileUrl);
            if (objectKey != null) {
                ossClient.deleteObject(ossConfig.getBucketName(), objectKey);
                logger.info("文件删除成功: {}", fileUrl);
            }
        } catch (Exception e) {
            logger.error("文件删除失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响主要业务流程
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex) : "";
    }

    /**
     * 从URL中提取对象键
     */
    private String extractObjectKey(String fileUrl) {
        if (fileUrl == null || !fileUrl.startsWith(ossConfig.getBaseUrl())) {
            return null;
        }
        return fileUrl.substring(ossConfig.getBaseUrl().length() + 1);
    }

    /**
     * 检查文件是否存在
     */
    public boolean doesFileExist(String fileUrl) {
        try {
            String objectKey = extractObjectKey(fileUrl);
            return objectKey != null && ossClient.doesObjectExist(ossConfig.getBucketName(), objectKey);
        } catch (Exception e) {
            logger.error("检查文件存在性失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
