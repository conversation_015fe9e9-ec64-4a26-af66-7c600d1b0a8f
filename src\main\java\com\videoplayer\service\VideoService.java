package com.videoplayer.service;

import com.videoplayer.entity.Video;
import com.videoplayer.repository.VideoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 视频服务类
 */
@Service
@Transactional
public class VideoService {

    @Autowired
    private VideoRepository videoRepository;

    @Autowired(required = false)
    private OssService ossService;

    /**
     * 获取所有视频（分页）
     */
    public List<Video> getAllVideos(int page, int size) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<Video> videoPage = videoRepository.findByIsActiveTrue(pageable);
        return videoPage.getContent();
    }

    /**
     * 搜索视频（分页，返回列表）
     */
    public List<Video> searchVideosList(String keyword, int page, int size) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(page, size, sort);
        return videoRepository.searchVideos(keyword, pageable).getContent();
    }

    /**
     * 获取所有视频（包括禁用的，用于管理页面）
     */
    public List<Video> getAllVideosForAdmin() {
        return videoRepository.findAll(Sort.by(Sort.Direction.DESC, "createdTime"));
    }

    /**
     * 分页获取所有启用的视频
     */
    public Page<Video> getAllActiveVideos(int page, int size, String sortBy, String sortDir) {
        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                   Sort.by(sortBy).descending() :
                   Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        return videoRepository.findByIsActiveTrue(pageable);
    }

    /**
     * 根据ID获取视频
     */
    public Optional<Video> getVideoById(Long id) {
        return videoRepository.findByIdAndIsActiveTrue(id);
    }

    /**
     * 保存视频
     */
    public Video saveVideo(Video video) {
        return videoRepository.save(video);
    }

    /**
     * 更新视频信息
     */
    public Video updateVideo(Long id, Video videoDetails) {
        Optional<Video> optionalVideo = videoRepository.findById(id);
        if (optionalVideo.isPresent()) {
            Video video = optionalVideo.get();
            video.setTitle(videoDetails.getTitle());
            video.setDescription(videoDetails.getDescription());
            video.setVideoUrl(videoDetails.getVideoUrl());
            video.setThumbnailUrl(videoDetails.getThumbnailUrl());
            video.setDuration(videoDetails.getDuration());
            video.setFileSize(videoDetails.getFileSize());
            video.setVideoFormat(videoDetails.getVideoFormat());
            video.setResolution(videoDetails.getResolution());
            return videoRepository.save(video);
        }
        return null;
    }

    /**
     * 删除视频（软删除）
     */
    public boolean deleteVideo(Long id) {
        Optional<Video> optionalVideo = videoRepository.findById(id);
        if (optionalVideo.isPresent()) {
            Video video = optionalVideo.get();
            video.setIsActive(false);
            videoRepository.save(video);
            return true;
        }
        return false;
    }

    /**
     * 彻底删除视频（包括OSS文件）
     */
    public boolean permanentDeleteVideo(Long id) {
        Optional<Video> optionalVideo = videoRepository.findById(id);
        if (optionalVideo.isPresent()) {
            Video video = optionalVideo.get();

            // 删除OSS文件
            if (ossService != null) {
                if (video.getVideoUrl() != null) {
                    ossService.deleteFile(video.getVideoUrl());
                }
                if (video.getThumbnailUrl() != null) {
                    ossService.deleteFile(video.getThumbnailUrl());
                }
            }

            // 从数据库删除
            videoRepository.delete(video);
            return true;
        }
        return false;
    }

    /**
     * 根据标题搜索视频
     */
    public List<Video> searchVideosByTitle(String title) {
        return videoRepository.findByTitleContainingIgnoreCase(title);
    }

    /**
     * 分页搜索视频
     */
    public Page<Video> searchVideos(String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        return videoRepository.searchVideos(keyword, pageable);
    }

    /**
     * 获取最新视频（按创建时间排序）
     */
    public List<Video> getPopularVideos(int limit) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(0, limit, sort);
        return videoRepository.findByIsActiveTrue(pageable).getContent();
    }

    /**
     * 获取视频总数
     */
    public long getTotalVideoCount() {
        return videoRepository.countByIsActiveTrue();
    }
}
