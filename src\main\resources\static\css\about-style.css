/**
 * 关于页面专用样式
 * About Page Styles
 */

/* 页面头部 */
.page-header {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 3rem;
    border-radius: 0 0 20px 20px;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.page-header-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* About页面Lead文字样式 - 清晰优雅 */
.page-header .lead {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    line-height: 1.4;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    background: rgba(255, 255, 255, 0.25);
    border-radius: 12px;
    padding: 1rem 2rem;
    border: 1px solid rgba(255, 255, 255, 0.4);
    display: inline-block;
    margin: 1rem 0;
    animation: fadeInScale 0.6s ease-out;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    transition: all 0.3s ease;
}

/* Lead悬停效果 */
.page-header .lead:hover {
    background: rgba(255, 255, 255, 0.35);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
}

/* 动画定义 */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 响应式优化 */
@media (max-width: 768px) {
    .page-header .lead {
        font-size: 1rem;
        padding: 0.8rem 1.5rem;
        letter-spacing: 0.3px;
    }
}

@media (max-width: 576px) {
    .page-header .lead {
        font-size: 0.9rem;
        padding: 0.7rem 1.2rem;
        letter-spacing: 0.2px;
        line-height: 1.3;
    }
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* 内容区域 */
.content-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.1);
    padding: 3rem;
    margin-bottom: 2rem;
    position: relative;
}

.content-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6f42c1, #e83e8c);
    border-radius: 15px 15px 0 0;
}

/* 章节标题 */
.section-title {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 2rem;
    position: relative;
    padding-bottom: 1rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #6f42c1, #e83e8c);
    border-radius: 2px;
}

/* 特性卡片 */
.feature-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    background: white;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

.feature-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-description {
    color: #6c757d;
    line-height: 1.6;
}

/* 团队成员卡片 */
.team-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.team-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.team-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 2rem auto 1rem;
    border: 4px solid #f8f9fa;
    transition: transform 0.3s ease;
}

.team-card:hover .team-avatar {
    transform: scale(1.05);
}

.team-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.team-role {
    color: #6f42c1;
    font-weight: 500;
    margin-bottom: 1rem;
}

.team-bio {
    color: #6c757d;
    padding: 0 1.5rem 2rem;
    line-height: 1.6;
}

/* 统计数据 */
.stats-section {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    color: white;
    border-radius: 15px;
    padding: 3rem 2rem;
    margin: 3rem 0;
    text-align: center;
}

.stat-item {
    padding: 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    display: block;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* 联系信息 */
.contact-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.contact-card:hover {
    border-color: #6f42c1;
    background: white;
    box-shadow: 0 5px 20px rgba(111, 66, 193, 0.1);
}

.contact-icon {
    font-size: 3rem;
    color: #6f42c1;
    margin-bottom: 1rem;
}

.contact-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.contact-info {
    color: #6c757d;
    font-size: 1.1rem;
}

/* 技术栈 */
.tech-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.tech-item {
    background: linear-gradient(135deg, #6f42c1, #e83e8c);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.tech-item:hover {
    transform: scale(1.05);
}

/* 时间线 */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, #6f42c1, #e83e8c);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -0.5rem;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    background: #6f42c1;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #6f42c1;
}

.timeline-date {
    color: #6f42c1;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.timeline-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.timeline-description {
    color: #6c757d;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        padding: 2rem 0;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .content-section {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }
    
    .feature-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .stats-section {
        padding: 2rem 1rem;
        margin: 2rem 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .team-avatar {
        width: 100px;
        height: 100px;
    }
    
    .timeline {
        padding-left: 1.5rem;
    }
    
    .timeline-item {
        padding-left: 1.5rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 1.5rem 0;
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
    
    .content-section {
        padding: 1.5rem 1rem;
        margin: 0.5rem;
    }
    
    .tech-stack {
        gap: 0.5rem;
    }
    
    .tech-item {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    .contact-card {
        padding: 1.5rem;
    }
    
    .contact-icon {
        font-size: 2rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .content-section {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .section-title {
        color: #f7fafc;
    }
    
    .feature-card {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .feature-card:hover {
        background: #2d3748;
    }
    
    .feature-title {
        color: #f7fafc;
    }
    
    .feature-description {
        color: #a0aec0;
    }
    
    .team-card {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .team-name {
        color: #f7fafc;
    }
    
    .team-bio {
        color: #a0aec0;
    }
    
    .contact-card {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .contact-card:hover {
        background: #2d3748;
        border-color: #6f42c1;
    }
    
    .contact-title {
        color: #f7fafc;
    }
    
    .contact-info {
        color: #a0aec0;
    }
    
    .timeline-title {
        color: #f7fafc;
    }
    
    .timeline-description {
        color: #a0aec0;
    }
}
