package com.videoplayer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;
import org.springframework.lang.NonNull;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 视频播放器应用程序主启动类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
public class VideoPlayerApplication implements ApplicationListener<WebServerInitializedEvent> {

    public static void main(String[] args) {
        SpringApplication.run(VideoPlayerApplication.class, args);
    }

    @Override
    public void onApplicationEvent(@NonNull WebServerInitializedEvent event) {
        try {
            Environment env = event.getApplicationContext().getEnvironment();
            String ip = InetAddress.getLocalHost().getHostAddress();
            String port = env.getProperty("local.server.port");
            String path = env.getProperty("server.servlet.context-path", "");
            
            System.out.println("\n----------------------------------------------------------");
            System.out.println("视频播放器应用启动成功！");
            System.out.println("本地访问: http://localhost:" + port + path);
            System.out.println("外部访问: http://" + ip + ":" + port + path);
            System.out.println("----------------------------------------------------------\n");
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
    }
}

