package com.videoplayer.exception;

/**
 * 资源未找到异常
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ResourceNotFoundException extends BusinessException {
    
    public ResourceNotFoundException(String message) {
        super("RESOURCE_NOT_FOUND", message);
    }
    
    public ResourceNotFoundException(String resourceName, Object resourceId) {
        super("RESOURCE_NOT_FOUND", String.format("%s with id %s not found", resourceName, resourceId));
    }
}
