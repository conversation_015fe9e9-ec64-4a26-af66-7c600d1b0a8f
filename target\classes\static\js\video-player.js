/**
 * 视频播放器增强功能
 * Video Player Enhancement Functions
 * <AUTHOR>
 * @version 1.0.0
 */

// 全局播放器实例
let globalPlayer = null;

/**
 * 初始化视频播放器增强功能
 */
function initializeVideoPlayerEnhancements() {
    // 添加键盘快捷键支持
    addKeyboardShortcuts();
    
    // 添加播放器事件监听
    addPlayerEventListeners();
    
    // 添加画中画支持
    addPictureInPictureSupport();
    
    console.log('视频播放器增强功能已初始化');
}

/**
 * 添加键盘快捷键支持
 */
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // 只在播放页面且播放器存在时响应快捷键
        if (!globalPlayer || document.activeElement.tagName === 'INPUT') {
            return;
        }

        switch(e.code) {
            case 'Space':
                e.preventDefault();
                togglePlayPause();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                seekBackward(10);
                break;
            case 'ArrowRight':
                e.preventDefault();
                seekForward(10);
                break;
            case 'ArrowUp':
                e.preventDefault();
                adjustVolume(0.1);
                break;
            case 'ArrowDown':
                e.preventDefault();
                adjustVolume(-0.1);
                break;
            case 'KeyF':
                e.preventDefault();
                toggleFullscreen();
                break;
            case 'KeyM':
                e.preventDefault();
                toggleMute();
                break;
        }
    });
}

/**
 * 添加播放器事件监听
 */
function addPlayerEventListeners() {
    // 等待播放器初始化完成
    setTimeout(() => {
        if (window.videojs && document.getElementById('video-player')) {
            globalPlayer = videojs('video-player');
            
            // 播放开始事件
            globalPlayer.on('play', function() {
                console.log('视频开始播放');
                updatePlayButton(true);
            });
            
            // 暂停事件
            globalPlayer.on('pause', function() {
                console.log('视频暂停');
                updatePlayButton(false);
            });
            
            // 时间更新事件
            globalPlayer.on('timeupdate', function() {
                updateProgressInfo();
            });
            
            // 音量变化事件
            globalPlayer.on('volumechange', function() {
                updateVolumeInfo();
            });
            
            // 全屏状态变化
            globalPlayer.on('fullscreenchange', function() {
                updateFullscreenButton();
            });
        }
    }, 1000);
}

/**
 * 添加画中画支持
 */
function addPictureInPictureSupport() {
    // 检查浏览器是否支持画中画
    if ('pictureInPictureEnabled' in document) {
        // 添加画中画按钮（如果Video.js没有自动添加）
        setTimeout(() => {
            if (globalPlayer && !document.querySelector('.vjs-picture-in-picture-control')) {
                addCustomPiPButton();
            }
        }, 1500);
    }
}

/**
 * 添加自定义画中画按钮
 */
function addCustomPiPButton() {
    const controlBar = document.querySelector('.vjs-control-bar');
    if (!controlBar) return;

    const pipButton = document.createElement('button');
    pipButton.className = 'vjs-control vjs-button custom-pip-button';
    pipButton.innerHTML = '<i class="fas fa-external-link-alt"></i>';
    pipButton.title = '画中画模式';
    pipButton.onclick = togglePictureInPicture;

    // 插入到全屏按钮之前
    const fullscreenButton = controlBar.querySelector('.vjs-fullscreen-control');
    if (fullscreenButton) {
        controlBar.insertBefore(pipButton, fullscreenButton);
    } else {
        controlBar.appendChild(pipButton);
    }
}

/**
 * 切换播放/暂停
 */
function togglePlayPause() {
    if (globalPlayer) {
        if (globalPlayer.paused()) {
            globalPlayer.play();
        } else {
            globalPlayer.pause();
        }
    }
}

/**
 * 向后快进
 */
function seekBackward(seconds) {
    if (globalPlayer) {
        const currentTime = globalPlayer.currentTime();
        globalPlayer.currentTime(Math.max(0, currentTime - seconds));
    }
}

/**
 * 向前快进
 */
function seekForward(seconds) {
    if (globalPlayer) {
        const currentTime = globalPlayer.currentTime();
        const duration = globalPlayer.duration();
        globalPlayer.currentTime(Math.min(duration, currentTime + seconds));
    }
}

/**
 * 调整音量
 */
function adjustVolume(delta) {
    if (globalPlayer) {
        const currentVolume = globalPlayer.volume();
        const newVolume = Math.max(0, Math.min(1, currentVolume + delta));
        globalPlayer.volume(newVolume);
    }
}

/**
 * 切换静音
 */
function toggleMute() {
    if (globalPlayer) {
        globalPlayer.muted(!globalPlayer.muted());
    }
}

/**
 * 切换画中画模式
 */
function togglePictureInPicture() {
    const videoElement = document.querySelector('#video-player video');
    if (!videoElement) return;

    if (document.pictureInPictureElement) {
        document.exitPictureInPicture();
    } else {
        videoElement.requestPictureInPicture().catch(error => {
            console.error('画中画模式启动失败:', error);
        });
    }
}

/**
 * 更新播放按钮状态
 */
function updatePlayButton(isPlaying) {
    const playButtons = document.querySelectorAll('.custom-play-button');
    playButtons.forEach(button => {
        const icon = button.querySelector('i');
        if (icon) {
            icon.className = isPlaying ? 'fas fa-pause' : 'fas fa-play';
        }
    });
}

/**
 * 更新进度信息
 */
function updateProgressInfo() {
    if (!globalPlayer) return;
    
    const currentTime = globalPlayer.currentTime();
    const duration = globalPlayer.duration();
    
    // 更新自定义进度显示（如果有）
    const progressInfo = document.querySelector('.custom-progress-info');
    if (progressInfo && duration) {
        const percentage = (currentTime / duration) * 100;
        progressInfo.textContent = `${formatTime(currentTime)} / ${formatTime(duration)} (${percentage.toFixed(1)}%)`;
    }
}

/**
 * 更新音量信息
 */
function updateVolumeInfo() {
    if (!globalPlayer) return;
    
    const volume = globalPlayer.volume();
    const isMuted = globalPlayer.muted();
    
    // 更新自定义音量显示（如果有）
    const volumeInfo = document.querySelector('.custom-volume-info');
    if (volumeInfo) {
        volumeInfo.textContent = isMuted ? '静音' : `音量: ${Math.round(volume * 100)}%`;
    }
}

/**
 * 更新全屏按钮状态
 */
function updateFullscreenButton() {
    const fullscreenButtons = document.querySelectorAll('.custom-fullscreen-button');
    const isFullscreen = globalPlayer && globalPlayer.isFullscreen();
    
    fullscreenButtons.forEach(button => {
        const icon = button.querySelector('i');
        if (icon) {
            icon.className = isFullscreen ? 'fas fa-compress' : 'fas fa-expand';
        }
        button.title = isFullscreen ? '退出全屏' : '进入全屏';
    });
}

/**
 * 格式化时间显示
 */
function formatTime(seconds) {
    if (!seconds || isNaN(seconds)) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

/**
 * 获取播放器实例
 */
function getPlayer() {
    return globalPlayer;
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保Video.js已加载
    setTimeout(initializeVideoPlayerEnhancements, 500);
});

// 导出函数到全局作用域
window.getPlayer = getPlayer;
window.togglePlayPause = togglePlayPause;
window.seekBackward = seekBackward;
window.seekForward = seekForward;
window.adjustVolume = adjustVolume;
window.toggleMute = toggleMute;
window.togglePictureInPicture = togglePictureInPicture;
