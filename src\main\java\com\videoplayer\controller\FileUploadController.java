package com.videoplayer.controller;

import com.videoplayer.common.ApiResponse;
import com.videoplayer.entity.Video;
import com.videoplayer.service.OssService;
import com.videoplayer.service.VideoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/upload")
@CrossOrigin(origins = "*")
public class FileUploadController {

    private static final Logger logger = LoggerFactory.getLogger(FileUploadController.class);

    @Autowired
    private OssService ossService;

    @Autowired
    private VideoService videoService;

    /**
     * 上传视频文件
     */
    @PostMapping("/video")
    public ResponseEntity<ApiResponse<Map<String, String>>> uploadVideo(
            @RequestParam("file") MultipartFile file) {
        
        logger.info("开始上传视频文件: {}", file.getOriginalFilename());

        try {
            String videoUrl = ossService.uploadVideo(file);
            
            Map<String, String> result = new HashMap<>();
            result.put("videoUrl", videoUrl);
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", String.valueOf(file.getSize()));
            
            return ResponseEntity.ok(ApiResponse.success("视频上传成功", result));
            
        } catch (Exception e) {
            logger.error("视频上传失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("VIDEO_UPLOAD_FAILED", "视频上传失败: " + e.getMessage()));
        }
    }

    /**
     * 上传缩略图文件
     */
    @PostMapping("/thumbnail")
    public ResponseEntity<ApiResponse<Map<String, String>>> uploadThumbnail(
            @RequestParam("file") MultipartFile file) {
        
        logger.info("开始上传缩略图文件: {}", file.getOriginalFilename());

        try {
            String thumbnailUrl = ossService.uploadThumbnail(file);
            
            Map<String, String> result = new HashMap<>();
            result.put("thumbnailUrl", thumbnailUrl);
            result.put("fileName", file.getOriginalFilename());
            
            return ResponseEntity.ok(ApiResponse.success("缩略图上传成功", result));
            
        } catch (Exception e) {
            logger.error("缩略图上传失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("THUMBNAIL_UPLOAD_FAILED", "缩略图上传失败: " + e.getMessage()));
        }
    }

    /**
     * 上传视频和缩略图（一次性上传）
     */
    @PostMapping("/video-with-thumbnail")
    public ResponseEntity<ApiResponse<Video>> uploadVideoWithThumbnail(
            @RequestParam("videoFile") MultipartFile videoFile,
            @RequestParam(value = "thumbnailFile", required = false) MultipartFile thumbnailFile,
            @RequestParam("title") String title,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "duration", required = false) Integer duration,
            @RequestParam(value = "videoFormat", required = false) String videoFormat,
            @RequestParam(value = "resolution", required = false) String resolution) {
        
        logger.info("开始上传视频和缩略图: {}", title);

        try {
            // 上传视频文件
            String videoUrl = ossService.uploadVideo(videoFile);
            
            // 上传缩略图（可选）
            String thumbnailUrl = null;
            if (thumbnailFile != null && !thumbnailFile.isEmpty()) {
                thumbnailUrl = ossService.uploadThumbnail(thumbnailFile);
            }

            // 创建视频记录
            Video video = new Video();
            video.setTitle(title);
            video.setDescription(description);
            video.setVideoUrl(videoUrl);
            video.setThumbnailUrl(thumbnailUrl);
            video.setDuration(duration);
            video.setFileSize(videoFile.getSize());
            video.setVideoFormat(videoFormat != null ? videoFormat : getVideoFormat(videoFile));
            video.setResolution(resolution);

            // 保存到数据库
            Video savedVideo = videoService.saveVideo(video);
            
            return ResponseEntity.ok(ApiResponse.success("视频上传并保存成功", savedVideo));
            
        } catch (Exception e) {
            logger.error("视频上传失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("VIDEO_UPLOAD_FAILED", "视频上传失败: " + e.getMessage()));
        }
    }

    /**
     * 获取视频格式
     */
    private String getVideoFormat(MultipartFile file) {
        String contentType = file.getContentType();
        if (contentType != null) {
            if (contentType.contains("mp4")) return "mp4";
            if (contentType.contains("avi")) return "avi";
            if (contentType.contains("mov")) return "mov";
            if (contentType.contains("wmv")) return "wmv";
            if (contentType.contains("flv")) return "flv";
            if (contentType.contains("webm")) return "webm";
        }
        
        String filename = file.getOriginalFilename();
        if (filename != null) {
            int lastDotIndex = filename.lastIndexOf('.');
            if (lastDotIndex > 0) {
                return filename.substring(lastDotIndex + 1).toLowerCase();
            }
        }
        
        return "mp4"; // 默认格式
    }
}
