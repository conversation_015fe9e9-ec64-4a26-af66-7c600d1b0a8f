/* Search Results Page Styles */

:root {
    --contact-margin-top: 0rem;
    --contact-margin-bottom: 1rem;
    --contact-height: 168px;
}

html, body {
    height: 100%;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main.container {
    flex: 1;
    margin-bottom: auto;
}

footer {
    margin-top: auto;
    flex-shrink: 0;
}

.search-highlight {
    background-color: #fff3cd;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
}

.spqk01 {
    margin: 2px !important;
}

/* 搜索容器布局优化 */
.search-container {
    gap: 0.5rem;
    flex-wrap: nowrap;
}

.search-container .search-form {
    flex: 1;
    min-width: 0;
}

.search-container .btn {
    flex-shrink: 0;
    white-space: nowrap;
}

/* 确保清除搜索按钮可见 */
.search-container .ms-1 {
    margin-left: 0.5rem !important;
    z-index: 10;
}


.btn.btn-outline-light.btn-sm.ms-1{
    margin: 0!important;
}
