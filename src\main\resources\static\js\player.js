/**
 * 视频播放器初始化和控制
 */

// 当页面加载完成时初始化视频播放器
document.addEventListener('DOMContentLoaded', function() {
    initializeVideoPlayer();
});

/**
 * 初始化视频播放器
 */
function initializeVideoPlayer() {
    const videoElement = document.getElementById('video-player');
    if (!videoElement) return;

    // 获取视频URL和ID
    const videoUrl = videoElement.getAttribute('data-video-url');
    const videoId = videoElement.getAttribute('data-video-id');

    if (!videoUrl) {
        console.error('视频URL未提供');
        return;
    }

    // 初始化Video.js播放器
    const player = videojs('video-player', {
        controls: true,
        autoplay: false,
        preload: 'auto',
        fluid: true,
        responsive: true,
        playbackRates: [0.5, 1, 1.5, 2],
        controlBar: {
            children: [
                'playToggle',
                'volumePanel',
                'currentTimeDisplay',
                'timeDivider',
                'durationDisplay',
                'progressControl',
                'playbackRateMenuButton',
                'fullscreenToggle'
            ]
        }
    });

    // 设置视频源
    player.src({
        type: 'video/mp4',
        src: videoUrl
    });

    // 错误处理
    player.on('error', function() {
        console.error('视频加载失败:', player.error());
        showAlert('视频加载失败，请检查网络连接或稍后重试', 'danger');
    });

    // 视频开始播放时
    player.on('play', function() {
        console.log('视频开始播放');
    });

    // 视频加载完成时
    player.on('loadedmetadata', function() {
        console.log('视频元数据加载完成');
    });

    // 保存到全局变量
    window.currentPlayer = player;
}



/**
 * 销毁播放器
 */
function destroyVideoPlayer() {
    if (window.currentPlayer) {
        window.currentPlayer.dispose();
        window.currentPlayer = null;
    }
}
