package com.videoplayer;

import com.videoplayer.common.ApiResponse;
import com.videoplayer.entity.Video;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ApiResponse单元测试
 * 简单的单元测试，不依赖Spring Boot上下文
 */
public class ApiResponseUnitTest {

    @Test
    public void testSuccessWithData() {
        Video video = new Video();
        video.setTitle("测试视频");
        
        ApiResponse<Video> response = ApiResponse.success("获取成功", video);
        
        assertTrue(response.isSuccess());
        assertEquals("获取成功", response.getMessage());
        assertEquals(video, response.getData());
    }

    @Test
    public void testSuccessWithList() {
        List<Video> videos = Arrays.asList(new Video(), new Video());
        
        ApiResponse<List<Video>> response = ApiResponse.success(videos, 2L, 0, 10);
        
        assertTrue(response.isSuccess());
        assertEquals("查询成功", response.getMessage());
        assertEquals(videos, response.getData());
        assertEquals(2L, response.getTotal());
        assertEquals(0, response.getPage());
        assertEquals(10, response.getSize());
    }

    @Test
    public void testSuccessWithString() {
        ApiResponse<String> response = ApiResponse.success("操作完成");
        
        assertTrue(response.isSuccess());
        assertEquals("操作完成", response.getMessage());
        assertEquals("操作完成", response.getData());
    }

    @Test
    public void testSuccessVoid() {
        ApiResponse<Void> response = ApiResponse.successVoid("清除完成");
        
        assertTrue(response.isSuccess());
        assertEquals("清除完成", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testSuccessVoidDefault() {
        ApiResponse<Void> response = ApiResponse.successVoid();
        
        assertTrue(response.isSuccess());
        assertEquals("操作成功", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testError() {
        ApiResponse<Void> response = ApiResponse.error("操作失败");
        
        assertFalse(response.isSuccess());
        assertEquals("操作失败", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testErrorWithCode() {
        ApiResponse<Void> response = ApiResponse.error("ERROR_CODE", "操作失败");
        
        assertFalse(response.isSuccess());
        assertEquals("操作失败", response.getMessage());
        assertEquals("ERROR_CODE", response.getErrorCode());
        assertNull(response.getData());
    }
}
