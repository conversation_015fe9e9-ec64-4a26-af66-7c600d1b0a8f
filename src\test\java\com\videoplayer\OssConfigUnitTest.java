package com.videoplayer;

import com.videoplayer.config.OssConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OSS配置单元测试 - 不依赖Spring上下文
 */
public class OssConfigUnitTest {

    private OssConfig ossConfig;

    @BeforeEach
    public void setUp() {
        ossConfig = new OssConfig();
        ossConfig.setEndpoint("https://oss-cn-test.aliyuncs.com");
        ossConfig.setAccessKeyId("test-key-id");
        ossConfig.setAccessKeySecret("test-key-secret");
        ossConfig.setBucketName("test-bucket");
        ossConfig.setVideoDir("test/videos");
        ossConfig.setThumbnailDir("test/thumbnails");
        ossConfig.setBaseUrl("https://test-bucket.oss-cn-test.aliyuncs.com");
    }

    @Test
    public void testOssConfigProperties() {
        assertNotNull(ossConfig);
        assertEquals("https://oss-cn-test.aliyuncs.com", ossConfig.getEndpoint());
        assertEquals("test-key-id", ossConfig.getAccessKeyId());
        assertEquals("test-key-secret", ossConfig.getAccessKeySecret());
        assertEquals("test-bucket", ossConfig.getBucketName());
        assertEquals("test/videos", ossConfig.getVideoDir());
        assertEquals("test/thumbnails", ossConfig.getThumbnailDir());
        assertEquals("https://test-bucket.oss-cn-test.aliyuncs.com", ossConfig.getBaseUrl());
    }

    @Test
    public void testOssConfigSettersAndGetters() {
        OssConfig config = new OssConfig();
        
        config.setEndpoint("https://oss-cn-beijing.aliyuncs.com");
        assertEquals("https://oss-cn-beijing.aliyuncs.com", config.getEndpoint());
        
        config.setAccessKeyId("new-key-id");
        assertEquals("new-key-id", config.getAccessKeyId());
        
        config.setAccessKeySecret("new-key-secret");
        assertEquals("new-key-secret", config.getAccessKeySecret());
        
        config.setBucketName("new-bucket");
        assertEquals("new-bucket", config.getBucketName());
        
        config.setVideoDir("new/videos");
        assertEquals("new/videos", config.getVideoDir());
        
        config.setThumbnailDir("new/thumbnails");
        assertEquals("new/thumbnails", config.getThumbnailDir());
        
        config.setBaseUrl("https://new-bucket.oss-cn-beijing.aliyuncs.com");
        assertEquals("https://new-bucket.oss-cn-beijing.aliyuncs.com", config.getBaseUrl());
    }
}
