/**
 * 佳茵轻康视频管理界面
 * 人性化的管理界面JavaScript功能
 * @version 2.0.0
 */

// 全局变量
let selectedVideos = new Set();
let allVideos = [];
let filteredVideos = [];

// 工具函数
const AdminUtils = {
    // 显示美观的提示消息
    showAlert: function(message, type = 'info', duration = 3000) {
        const alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) return;

        const alertTypes = {
            'success': { class: 'alert-success', icon: 'fas fa-check-circle' },
            'danger': { class: 'alert-danger', icon: 'fas fa-exclamation-circle' },
            'warning': { class: 'alert-warning', icon: 'fas fa-exclamation-triangle' },
            'info': { class: 'alert-info', icon: 'fas fa-info-circle' }
        };

        const alertType = alertTypes[type] || alertTypes.info;
        const alertId = 'alert-' + Date.now();

        const alertDiv = document.createElement('div');
        alertDiv.id = alertId;
        alertDiv.className = `alert ${alertType.class} alert-dismissible fade show shadow-sm`;
        alertDiv.innerHTML = `
            <i class="${alertType.icon} me-2"></i>
            <strong>${message}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        alertContainer.appendChild(alertDiv);

        // 自动消失
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, duration);
    },



    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 复制到剪贴板
    copyToClipboard: function(text) {
        // 直接使用降级方案，避免与浏览器扩展冲突
        this.fallbackCopyTextToClipboard(text);
    },

    // 降级复制方案
    fallbackCopyTextToClipboard: function(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            this.showAlert('链接已复制到剪贴板 📋', 'success');
        } catch (err) {
            this.showAlert('复制失败，请手动复制', 'warning');
        }

        document.body.removeChild(textArea);
    },

    // 格式化时间
    formatDuration: function(seconds) {
        if (!seconds) return '未知';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
};

// API接口函数
const AdminApi = {
    async createVideo(videoData) {
        try {
            const response = await fetch('/api/videos', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(videoData)
            });
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    },

    // 上传视频文件
    async uploadVideo(file, onProgress) {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/upload/video', {
                method: 'POST',
                body: formData
            });
            return await response.json();
        } catch (error) {
            throw new Error('视频上传失败：' + error.message);
        }
    },

    // 上传缩略图
    async uploadThumbnail(file) {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/upload/thumbnail', {
                method: 'POST',
                body: formData
            });
            return await response.json();
        } catch (error) {
            throw new Error('缩略图上传失败：' + error.message);
        }
    },

    // 一次性上传视频和缩略图
    async uploadVideoWithThumbnail(formData) {
        try {
            const response = await fetch('/api/upload/video-with-thumbnail', {
                method: 'POST',
                body: formData
            });
            return await response.json();
        } catch (error) {
            throw new Error('视频上传失败：' + error.message);
        }
    },

    async updateVideo(id, videoData) {
        try {
            const response = await fetch(`/api/videos/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(videoData)
            });
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    },

    async deleteVideo(id) {
        try {
            const response = await fetch(`/api/videos/${id}`, {
                method: 'DELETE'
            });
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    },

    async getVideos(page = 0, size = 100) {
        try {
            const response = await fetch(`/api/videos?page=${page}&size=${size}`);
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    },

    async toggleVideoStatus(id, isActive) {
        try {
            const response = await fetch(`/api/videos/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ isActive: !isActive })
            });
            return await response.json();
        } catch (error) {
            throw new Error('网络错误：' + error.message);
        }
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminPage();
    setupKeyboardShortcuts();
    updateStatistics();
});

// 初始化管理页面
function initializeAdminPage() {
    console.log('🚀 初始化管理界面...');

    // 初始化搜索功能
    initializeSearch();

    // 初始化选择功能
    initializeSelection();

    // 初始化筛选功能
    initializeFilters();

    // 初始化删除确认
    initializeDeleteConfirmation();

    // 收集所有视频数据
    collectVideoData();

    AdminUtils.showAlert('管理界面已就绪 ✨', 'success', 2000);
}

// 收集视频数据
function collectVideoData() {
    const videoRows = document.querySelectorAll('.video-row');
    allVideos = Array.from(videoRows).map(row => ({
        id: row.dataset.videoId,
        element: row,
        title: row.querySelector('.video-title')?.textContent || '',
        description: row.querySelector('.video-description')?.textContent || '',
        isActive: !row.classList.contains('disabled')
    }));
    filteredVideos = [...allVideos];
    console.log(`📊 收集到 ${allVideos.length} 个视频`);
}

// 初始化搜索功能
function initializeSearch() {
    const searchInput = document.getElementById('adminSearch');
    if (searchInput) {
        searchInput.addEventListener('input', AdminUtils.debounce(function() {
            performSearch(this.value);
        }, 300));

        // 搜索框获得焦点时的提示
        searchInput.addEventListener('focus', function() {
            this.placeholder = '输入关键词开始搜索...';
        });

        searchInput.addEventListener('blur', function() {
            this.placeholder = '输入标题或描述关键词...';
        });
    }
}

// 执行搜索
function performSearch(keyword) {
    if (!keyword.trim()) {
        // 显示所有视频
        filteredVideos = [...allVideos];
        showAllVideos();
        updateFilteredCount();
        return;
    }

    const searchTerm = keyword.toLowerCase();
    filteredVideos = allVideos.filter(video =>
        video.title.toLowerCase().includes(searchTerm) ||
        video.description.toLowerCase().includes(searchTerm)
    );

    // 显示搜索结果
    showFilteredVideos();
    updateFilteredCount();

    if (filteredVideos.length === 0) {
        AdminUtils.showAlert(`没有找到包含"${keyword}"的视频 🔍`, 'info');
    }
}

// 显示所有视频
function showAllVideos() {
    allVideos.forEach(video => {
        video.element.style.display = '';
    });
}

// 显示筛选后的视频
function showFilteredVideos() {
    const filteredIds = new Set(filteredVideos.map(v => v.id));

    allVideos.forEach(video => {
        video.element.style.display = filteredIds.has(video.id) ? '' : 'none';
    });
}

// 更新筛选计数
function updateFilteredCount() {
    const filteredCountElement = document.getElementById('filteredCount');
    if (filteredCountElement) {
        if (filteredVideos.length !== allVideos.length) {
            filteredCountElement.style.display = 'inline';
            filteredCountElement.querySelector('span').textContent = filteredVideos.length;
        } else {
            filteredCountElement.style.display = 'none';
        }
    }
}

// 初始化选择功能
function initializeSelection() {
    // 全选复选框
    const selectAllCheckbox = document.getElementById('selectAllTable');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            const visibleCheckboxes = document.querySelectorAll('.video-checkbox:not([style*="display: none"])');

            visibleCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                updateVideoSelection(checkbox.value, isChecked);
            });

            updateSelectionUI();
        });
    }

    // 单个视频复选框
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('video-checkbox')) {
            updateVideoSelection(e.target.value, e.target.checked);
            updateSelectionUI();
            updateSelectAllState();
        }
    });
}

// 更新视频选择状态
function updateVideoSelection(videoId, isSelected) {
    if (isSelected) {
        selectedVideos.add(videoId);
    } else {
        selectedVideos.delete(videoId);
    }
}

// 更新选择相关UI
function updateSelectionUI() {
    const selectedCount = selectedVideos.size;

    // 更新选中计数
    const selectedCountElement = document.getElementById('selectedCount');
    if (selectedCountElement) {
        selectedCountElement.textContent = selectedCount;
    }

    // 更新选中信息显示
    const selectedInfo = document.getElementById('selectedInfo');
    if (selectedInfo) {
        if (selectedCount > 0) {
            selectedInfo.style.display = 'inline';
            selectedInfo.querySelector('span').textContent = selectedCount;
        } else {
            selectedInfo.style.display = 'none';
        }
    }

    // 更新批量操作按钮状态
    const batchButtons = document.querySelectorAll('.batch-operation');
    batchButtons.forEach(button => {
        button.disabled = selectedCount === 0;
    });

    // 高亮选中的行
    document.querySelectorAll('.video-row').forEach(row => {
        const videoId = row.dataset.videoId;
        if (selectedVideos.has(videoId)) {
            row.classList.add('table-primary');
        } else {
            row.classList.remove('table-primary');
        }
    });
}

// 更新全选状态
function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('selectAllTable');
    if (!selectAllCheckbox) return;

    const visibleCheckboxes = document.querySelectorAll('.video-checkbox:not([style*="display: none"])');
    const checkedBoxes = document.querySelectorAll('.video-checkbox:checked:not([style*="display: none"])');

    if (checkedBoxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedBoxes.length === visibleCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }
}

// 选择操作函数
function selectAll() {
    const visibleCheckboxes = document.querySelectorAll('.video-checkbox:not([style*="display: none"])');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
        updateVideoSelection(checkbox.value, true);
    });
    updateSelectionUI();
    updateSelectAllState();
    AdminUtils.showAlert(`已选择 ${selectedVideos.size} 个视频`, 'info');
}

function selectNone() {
    document.querySelectorAll('.video-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    selectedVideos.clear();
    updateSelectionUI();
    updateSelectAllState();
    AdminUtils.showAlert('已取消所有选择', 'info');
}

function selectInvert() {
    const visibleCheckboxes = document.querySelectorAll('.video-checkbox:not([style*="display: none"])');
    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = !checkbox.checked;
        updateVideoSelection(checkbox.value, checkbox.checked);
    });
    updateSelectionUI();
    updateSelectAllState();
    AdminUtils.showAlert(`已反选，当前选择 ${selectedVideos.size} 个视频`, 'info');
}

// 初始化筛选功能
function initializeFilters() {
    const statusFilter = document.getElementById('statusFilter');
    const formatFilter = document.getElementById('formatFilter');
    const sortBy = document.getElementById('sortBy');

    if (statusFilter) {
        statusFilter.addEventListener('change', applyFilters);
    }

    if (formatFilter) {
        formatFilter.addEventListener('change', applyFilters);
    }

    if (sortBy) {
        sortBy.addEventListener('change', applyFilters);
    }
}

// 应用筛选器
function applyFilters() {
    const searchValue = document.getElementById('adminSearch')?.value || '';
    const statusValue = document.getElementById('statusFilter')?.value || '';
    const formatValue = document.getElementById('formatFilter')?.value || '';
    const sortValue = document.getElementById('sortBy')?.value || 'createdTime';

    // 构建URL参数
    const params = new URLSearchParams();
    if (searchValue) params.append('search', searchValue);
    if (statusValue) params.append('status', statusValue);
    if (formatValue) params.append('format', formatValue);
    if (sortValue !== 'createdTime') params.append('sortBy', sortValue);



    // 跳转到筛选后的页面
    const newUrl = `/admin${params.toString() ? '?' + params.toString() : ''}`;
    window.location.href = newUrl;
}

// 重置筛选器
function resetFilters() {
    document.getElementById('adminSearch').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('formatFilter').value = '';
    document.getElementById('sortBy').value = 'createdTime';

    AdminUtils.showAlert('筛选器已重置', 'info');
    setTimeout(() => {
        window.location.href = '/admin';
    }, 1000);
}

// 清除搜索
function clearSearch() {
    const searchInput = document.getElementById('adminSearch');
    if (searchInput) {
        searchInput.value = '';
        performSearch('');
        searchInput.focus();
    }
}

// 视频操作函数

// 复制视频链接
function copyVideoUrl(videoId) {
    const url = `${window.location.origin}/play/${videoId}`;
    AdminUtils.copyToClipboard(url);
}

// 确认删除视频
function confirmDeleteVideo(videoId, videoTitle) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const titleElement = document.getElementById('deleteVideoTitle');

    if (titleElement) {
        titleElement.textContent = videoTitle;
    }

    // 存储要删除的视频ID
    window.currentDeleteVideoId = videoId;

    modal.show();
}

// 切换视频状态
async function toggleVideoStatus(videoId, currentStatus) {
    try {


        const response = await AdminApi.toggleVideoStatus(videoId, currentStatus);

        if (response.success) {
            const statusText = currentStatus ? '禁用' : '启用';
            AdminUtils.showAlert(`视频已${statusText} ✅`, 'success');

            // 延迟刷新页面以显示变化
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            throw new Error(response.message || '操作失败');
        }
    } catch (error) {
        AdminUtils.showAlert('操作失败：' + error.message, 'danger');
    }
}

// 批量启用
async function batchEnable() {
    if (selectedVideos.size === 0) {
        AdminUtils.showAlert('请先选择要启用的视频', 'warning');
        return;
    }

    if (!confirm(`确定要启用选中的 ${selectedVideos.size} 个视频吗？`)) {
        return;
    }

    try {

        let successCount = 0;

        for (const videoId of selectedVideos) {
            try {
                const response = await AdminApi.toggleVideoStatus(videoId, false); // 启用
                if (response.success) successCount++;
            } catch (error) {
                console.error(`启用视频 ${videoId} 失败:`, error);
            }
        }

        AdminUtils.showAlert(`成功启用 ${successCount} 个视频 🎉`, 'success');
        setTimeout(() => window.location.reload(), 1500);

    } catch (error) {
        AdminUtils.showAlert('批量启用失败：' + error.message, 'danger');
    }
}

// 批量禁用
async function batchDisable() {
    if (selectedVideos.size === 0) {
        AdminUtils.showAlert('请先选择要禁用的视频', 'warning');
        return;
    }

    if (!confirm(`确定要禁用选中的 ${selectedVideos.size} 个视频吗？`)) {
        return;
    }

    try {

        let successCount = 0;

        for (const videoId of selectedVideos) {
            try {
                const response = await AdminApi.toggleVideoStatus(videoId, true); // 禁用
                if (response.success) successCount++;
            } catch (error) {
                console.error(`禁用视频 ${videoId} 失败:`, error);
            }
        }

        AdminUtils.showAlert(`成功禁用 ${successCount} 个视频 ⏸️`, 'success');
        setTimeout(() => window.location.reload(), 1500);

    } catch (error) {
        AdminUtils.showAlert('批量禁用失败：' + error.message, 'danger');
    }
}

// 批量删除
async function batchDelete() {
    if (selectedVideos.size === 0) {
        AdminUtils.showAlert('请先选择要删除的视频', 'warning');
        return;
    }

    const confirmMessage = `⚠️ 危险操作确认\n\n您即将删除 ${selectedVideos.size} 个视频。\n此操作不可撤销，确定继续吗？`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {

        let successCount = 0;
        let failCount = 0;

        for (const videoId of selectedVideos) {
            try {
                const response = await AdminApi.deleteVideo(videoId);
                if (response.success) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (error) {
                failCount++;
                console.error(`删除视频 ${videoId} 失败:`, error);
            }
        }

        if (successCount > 0) {
            AdminUtils.showAlert(`成功删除 ${successCount} 个视频 🗑️`, 'success');
        }

        if (failCount > 0) {
            AdminUtils.showAlert(`${failCount} 个视频删除失败`, 'warning');
        }

        setTimeout(() => window.location.reload(), 2000);

    } catch (error) {
        AdminUtils.showAlert('批量删除失败：' + error.message, 'danger');
    }
}

// 初始化删除确认
function initializeDeleteConfirmation() {
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', async function() {
            const videoId = window.currentDeleteVideoId;
            if (!videoId) return;

            try {


                const response = await AdminApi.deleteVideo(videoId);

                if (response.success) {
                    AdminUtils.showAlert('视频删除成功 🗑️', 'success');

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                    modal.hide();

                    // 刷新页面
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    throw new Error(response.message || '删除失败');
                }
            } catch (error) {
                AdminUtils.showAlert('删除失败：' + error.message, 'danger');
            }
        });
    }
}

// 更新统计信息
function updateStatistics() {
    const activeCount = document.querySelectorAll('.video-row:not(.disabled)').length;
    const inactiveCount = document.querySelectorAll('.video-row.disabled').length;

    const activeCountElement = document.getElementById('activeCount');
    const inactiveCountElement = document.getElementById('inactiveCount');

    if (activeCountElement) activeCountElement.textContent = activeCount;
    if (inactiveCountElement) inactiveCountElement.textContent = inactiveCount;
}

// 设置键盘快捷键
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+A 全选
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            selectAll();
        }

        // Delete 删除选中
        if (e.key === 'Delete' && selectedVideos.size > 0) {
            e.preventDefault();
            batchDelete();
        }

        // Escape 取消选择
        if (e.key === 'Escape') {
            selectNone();
        }

        // F5 刷新（允许默认行为）
        if (e.key === 'F5') {
            AdminUtils.showAlert('正在刷新页面...', 'info', 1000);
        }
    });
}

// 其他辅助功能

// 刷新页面
function refreshPage() {
    AdminUtils.showAlert('正在刷新页面...', 'info', 1000);
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// 显示帮助
function showHelp() {
    const modal = new bootstrap.Modal(document.getElementById('helpModal'));
    modal.show();
}

// 导出数据
function exportData() {
    AdminUtils.showAlert('数据导出功能开发中...', 'info');
}

// 导入数据
function importData() {
    AdminUtils.showAlert('数据导入功能开发中...', 'info');
}

// 预览视频（如果在添加/编辑页面）
function previewVideo() {
    const videoUrl = document.getElementById('videoUrl')?.value;
    if (!videoUrl) {
        AdminUtils.showAlert('请先输入视频链接', 'warning');
        return;
    }

    // 在新窗口中打开视频预览
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>视频预览 - 佳茵轻康</title>
            <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
            <style>
                body {
                    margin: 0;
                    padding: 20px;
                    background: #f8f9fa;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 10px;
                    padding: 20px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .video-js {
                    width: 100%;
                    height: 400px;
                    border-radius: 8px;
                }
                h1 {
                    color: #333;
                    margin-bottom: 20px;
                    text-align: center;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎬 视频预览</h1>
                <video id="preview-player" class="video-js vjs-default-skin" controls preload="auto">
                    <source src="${videoUrl}" type="video/mp4">
                    <p>您的浏览器不支持视频播放。</p>
                </video>
            </div>
            <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>
            <script>
                videojs('preview-player', {
                    fluid: true,
                    responsive: true,
                    playbackRates: [0.5, 1, 1.25, 1.5, 2]
                });
            </script>
        </body>
        </html>
    `);

    AdminUtils.showAlert('视频预览已在新窗口打开 🎬', 'success');
}

// 表单提交函数（用于添加/编辑页面）

/**
 * 提交添加视频表单
 */
async function submitAddVideo(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const videoData = Object.fromEntries(formData.entries());

    try {
        // 验证表单
        validateTitle(videoData.title);
        validateVideoUrl(videoData.videoUrl);
        validateDescription(videoData.description);


        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '保存中...';
        submitBtn.disabled = true;

        // 提交数据
        const response = await AdminApi.createVideo(videoData);

        if (response.success) {
            AdminUtils.showAlert('视频添加成功！🎉', 'success');
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1500);
        } else {
            throw new Error(response.message || '添加失败');
        }
    } catch (error) {
        AdminUtils.showAlert(error.message, 'danger');

        // 恢复按钮状态
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }
}

/**
 * 提交编辑视频表单
 */
async function submitEditVideo(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const videoData = Object.fromEntries(formData.entries());
    const videoId = document.getElementById('videoId').value;

    try {
        // 验证表单
        validateTitle(videoData.title);
        validateVideoUrl(videoData.videoUrl);
        validateDescription(videoData.description);


        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '保存中...';
        submitBtn.disabled = true;

        // 提交数据
        const response = await AdminApi.updateVideo(videoId, videoData);

        if (response.success) {
            AdminUtils.showAlert('视频更新成功！✨', 'success');
            setTimeout(() => {
                window.location.href = '/admin';
            }, 1500);
        } else {
            throw new Error(response.message || '更新失败');
        }
    } catch (error) {
        AdminUtils.showAlert(error.message, 'danger');

        // 恢复按钮状态
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }
}

/**
 * 删除当前视频（用于编辑页面）
 */
function deleteCurrentVideo() {
    const videoId = document.getElementById('videoId')?.value;
    const title = document.getElementById('title')?.value || '此视频';

    if (confirm(`确定要删除视频"${title}"吗？此操作不可撤销。`)) {
        confirmDeleteVideo(videoId, title);
    }
}

// 表单验证函数

/**
 * 验证标题
 */
function validateTitle(title) {
    if (!title || title.trim() === '') {
        throw new Error('视频标题不能为空 📝');
    }
    if (title.length > 200) {
        throw new Error('视频标题不能超过200个字符 📏');
    }
    return true;
}

/**
 * 验证视频URL
 */
function validateVideoUrl(url) {
    if (!url || url.trim() === '') {
        throw new Error('视频链接不能为空 🔗');
    }

    // 简单的URL格式验证
    const urlPattern = /^https?:\/\/.+/;
    if (!urlPattern.test(url)) {
        throw new Error('请输入有效的视频链接（以http://或https://开头）🌐');
    }

    return true;
}

/**
 * 验证描述
 */
function validateDescription(description) {
    if (description && description.length > 500) {
        throw new Error('视频描述不能超过500个字符 📄');
    }
    return true;
}

// 控制台输出欢迎信息
console.log(`
🎬 佳茵轻康视频管理系统
📅 版本: 2.0.0
🚀 状态: 已就绪
💡 提示: 使用 Ctrl+A 全选，Delete 删除，Esc 取消选择
`);

// 导出全局函数供HTML调用
window.AdminUtils = AdminUtils;
window.AdminApi = AdminApi;


