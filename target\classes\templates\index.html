<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <title th:text="${pageTitle}">视频播放器 - 首页</title>



    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">



    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/index-style.css" rel="stylesheet">

    <!-- 简化的样式 -->
    <style>
        :root {
            --contact-margin-top: 0rem;
            --contact-margin-bottom: 1rem;
            --contact-height: 168px;
        }

        /* 确保页脚置底的样式 */
        html, body {
            height: 100%;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        main.container {
            flex: 1;
            margin-bottom: auto;
        }

        footer {
            margin-top: auto;
            flex-shrink: 0;
        }
    </style>
</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top" style="background-color: #0d6efd !important; color-scheme: light only !important;">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <ul class="navbar-nav d-flex flex-row mb-0">
                    <li class="nav-item">
                        <a class="nav-link active px-2" href="/" style="color: #ffffff !important; color-scheme: light only !important;">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link px-2" href="/videos" style="color: rgba(255, 255, 255, 0.75) !important; color-scheme: light only !important;">
                            <i class="fas fa-video me-1"></i>视频
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link px-2" href="/admin" style="color: rgba(255, 255, 255, 0.75) !important; color-scheme: light only !important;">
                            <i class="fas fa-cog me-1"></i>管理
                        </a>
                    </li>
                </ul>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索..." style="background-color: rgba(255, 255, 255, 0.1) !important; border-color: rgba(255, 255, 255, 0.3) !important; color: #ffffff !important; color-scheme: light only !important;">
                        <button class="btn btn-outline-light btn-sm" type="submit" style="border-color: rgba(255, 255, 255, 0.5) !important; color: rgba(255, 255, 255, 0.75) !important; color-scheme: light only !important;">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()" style="border-color: rgba(255, 255, 255, 0.5) !important; color: rgba(255, 255, 255, 0.75) !important; color-scheme: light only !important;">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()" style="border-color: rgba(255, 255, 255, 0.5) !important; color: rgba(255, 255, 255, 0.75) !important; color-scheme: light only !important;">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container">
        <!-- 欢迎横幅 -->
        <section class="hero-section bg-gradient text-dark rounded-3 p-5 mb-5">
            <div class="col-12 text-center">
                <div class="title-with-logo">
                    <img src="/images/favicon.ico" alt="Logo" width="150" height="150">
                    <h1 class="display-4">佳茵轻康</h1>
                </div>
                <div class="lead-contact-display">
                    <p>微信/电话：18722880704【林佳】<br>抖音号：黄林佳【皮皮管理】</p>
                    <p>微信/电话：18057722960【黄超】<br>抖音号：黄超(黄小燕弟弟)</p>
                    <p>微信/电话：15908542510【小班】<br>抖音号：佳茵轻康SOS小班</p>
                </div>
            </div>
        </section>

        <!-- 视频内容区域 -->
        <section class="video-content">
            <!-- 有视频时显示 -->
            <div th:if="${popularVideos != null and !popularVideos.isEmpty()}">
                <header class="mb-4 d-flex justify-content-between align-items-center">
                    <h2 class="section-title mb-0">
                        <i class="fas fa-fire text-danger me-2"></i>教学视频
                    </h2>
                    <a href="/videos" class="btn btn-outline-primary">查看全部</a>
                </header>

                <div class="video-grid">
                    <article class="video-card card h-100 shadow-sm" th:each="video : ${popularVideos}">
                        <a th:href="@{/play/{id}(id=${video.id})}" class="video-thumbnail text-decoration-none">
                            <img th:src="${video.thumbnailUrl}"
                                 class="card-img-top thumbnail-optimized"
                                 th:alt="${video.title}"
                                 loading="lazy"
                                 decoding="async"
                                 th:data-fallback-src="${video.thumbnailUrl}"
                                 onerror="this.src=this.getAttribute('data-fallback-src')"
                                 style="height: 200px; object-fit: cover;"
                                 onload="this.classList.add('loaded')"
                                 data-loaded="false">
                        </a>
                        <div class="card-body">
                            <h3 class="card-title h5" th:text="${video.title}">佳茵轻康</h3>
                            <div class="video-stats">
                                <time class="video-date" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</time>
                            </div>
                        </div>
                    </article>
                </div>
            </div>

            <!-- 空状态 -->
            <div th:if="${popularVideos == null or popularVideos.isEmpty()}"
                 class="empty-state text-center py-5">
                <i class="fas fa-video fa-4x text-muted mb-4"></i>
                <h2 class="text-muted mb-4">暂无视频</h2>
                <p class="text-muted mb-4">还没有上传任何视频，点击下方按钮开始添加视频吧！</p>
                <a href="/admin/add" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>添加第一个视频
                </a>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="footer-single-container">
            <p class="spqk01">
                <span>轻康自然，享瘦生活。</span>
            </p>
            <p  class="spqk02">
                <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">点这里：联系我们</a></small>
            </p>
        </div>
    </footer>
    <!-- 简化的JavaScript -->
    <script>
        // 联系信息位置调整函数
        function adjustContact(top, bottom, height) {
            const root = document.documentElement;
            if (top !== undefined) root.style.setProperty('--contact-margin-top', top);
            if (bottom !== undefined) root.style.setProperty('--contact-margin-bottom', bottom);
            if (height !== undefined) root.style.setProperty('--contact-height', height);
        }
    </script>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/main.js"></script>




</body>
</html>

