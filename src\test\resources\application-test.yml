# 测试环境配置
spring:
  # 数据库配置 - 测试环境
  datasource:
    url: ******************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver

  # JPA配置 - 测试环境
  jpa:
    hibernate:
      ddl-auto: validate  # 测试时只验证，不修改数据库结构
    show-sql: false       # 测试时不显示SQL
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: false

  # 日志配置 - 测试环境
  logging:
    level:
      com.videoplayer: DEBUG
      org.springframework.web: WARN
      org.hibernate: WARN

# 阿里云OSS配置 - 测试环境
aliyun:
  oss:
    endpoint: https://oss-cn-test.aliyuncs.com
    access-key-id: test-key-id
    access-key-secret: test-key-secret
    bucket-name: test-bucket
    video-dir: test/videos
    thumbnail-dir: test/thumbnails
    base-url: https://test-bucket.oss-cn-test.aliyuncs.com
