 阿里云OSS配置指南

本文档将指导您如何配置阿里云OSS，实现视频文件的上传和播放功能。

## 📋 前置条件

1. 拥有阿里云账号
2. 已开通对象存储OSS服务
3. 已创建OSS存储桶（Bucket）

## 🚀 快速配置

### 1. 获取OSS配置信息

登录阿里云控制台，进入OSS管理页面：

1. **访问端点（Endpoint）**：
   - 进入您的存储桶详情页
   - 在"概览"页面找到"访问域名"
   - 选择外网访问域名，格式如：`https://oss-cn-guangzhou.aliyuncs.com`

2. **存储桶名称（Bucket Name）**：
   - 您创建的存储桶名称

3. **访问密钥（Access Key）**：
   - 进入"访问控制RAM" > "用户管理"
   - 创建新用户或使用现有用户
   - 为用户添加OSS相关权限
   - 获取AccessKey ID和AccessKey Secret

### 2. 配置应用程序

1. 复制项目根目录下的 `.env.example` 文件为 `.env`：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入您的OSS配置：
```bash
# OSS访问端点
OSS_ENDPOINT=https://oss-cn-guangzhou.aliyuncs.com

# OSS访问密钥
OSS_ACCESS_KEY_ID=your-actual-access-key-id
OSS_ACCESS_KEY_SECRET=your-actual-access-key-secret

# OSS存储桶名称
OSS_BUCKET_NAME=your-actual-bucket-name

# 文件存储目录（可自定义）
OSS_VIDEO_DIR=video-player/videos
OSS_THUMBNAIL_DIR=video-player/thumbnails

# OSS基础URL
OSS_BASE_URL=https://your-actual-bucket-name.oss-cn-guangzhou.aliyuncs.com
```

### 3. 设置存储桶权限

为了确保视频能够正常播放，需要设置存储桶的读取权限：

1. 进入OSS控制台 > 您的存储桶 > 权限管理 > 读写权限
2. 设置为"公共读"或"公共读写"
3. 或者配置更精细的访问策略

### 4. 配置跨域访问（CORS）

如果需要在网页中直接播放视频，建议配置CORS：

1. 进入OSS控制台 > 您的存储桶 > 权限管理 > 跨域设置
2. 添加跨域规则：
   - 来源：`*` 或您的域名
   - 允许Methods：`GET, POST, PUT, DELETE, HEAD`
   - 允许Headers：`*`
   - 暴露Headers：`ETag, x-oss-request-id`

## 🔧 高级配置

### 1. CDN加速（推荐）

为了提升视频播放速度，建议配置CDN：

1. 开通阿里云CDN服务
2. 添加加速域名，源站设置为您的OSS域名
3. 更新 `OSS_BASE_URL` 为CDN域名

### 2. 图片处理

OSS支持实时图片处理，可用于生成不同尺寸的缩略图：

```bash
# 在图片URL后添加处理参数
https://your-bucket.oss-cn-guangzhou.aliyuncs.com/image.jpg?x-oss-process=image/resize,w_300,h_200
```

### 3. 视频处理

OSS也支持视频处理服务，可用于：
- 视频转码
- 视频截图
- 视频水印

## 🛡️ 安全建议

### 1. 使用RAM子账号

不要使用主账号的AccessKey，而是：
1. 创建RAM子账号
2. 只授予必要的OSS权限
3. 定期轮换AccessKey

### 2. 权限最小化

为RAM用户分配最小必要权限：
```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "oss:PutObject",
        "oss:GetObject",
        "oss:DeleteObject"
      ],
      "Resource": [
        "acs:oss:*:*:your-bucket-name/video-player/*"
      ]
    }
  ]
}
```

### 3. 防盗链设置

配置Referer防盗链，防止资源被盗用：
1. 进入OSS控制台 > 您的存储桶 > 权限管理 > 防盗链
2. 设置允许的Referer列表

## 📝 测试配置

配置完成后，可以通过以下方式测试：

1. 启动应用程序
2. 访问 `/admin/add` 页面
3. 尝试上传一个小视频文件
4. 检查OSS控制台是否有文件上传
5. 尝试播放上传的视频

## 🔍 故障排除

### 常见问题

1. **上传失败**：
   - 检查AccessKey是否正确
   - 确认存储桶名称无误
   - 检查网络连接

2. **视频无法播放**：
   - 确认存储桶读取权限
   - 检查CORS配置
   - 验证视频文件格式

3. **访问被拒绝**：
   - 检查RAM用户权限
   - 确认存储桶策略设置

### 日志查看

应用程序会输出详细的OSS操作日志，可以通过日志排查问题：

```bash
# 查看应用日志
tail -f logs/application.log | grep OSS
```

## 📞 技术支持

如果遇到问题，可以：
1. 查看阿里云OSS官方文档
2. 联系阿里云技术支持
3. 在项目中提交Issue

---

配置完成后，您就可以享受高效、稳定的视频存储和播放服务了！🎉
