package com.videoplayer;

import com.videoplayer.config.OssConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OSS配置测试
 */
@SpringBootTest
@TestPropertySource(properties = {
    // OSS测试配置
    "aliyun.oss.endpoint=https://oss-cn-test.aliyuncs.com",
    "aliyun.oss.access-key-id=test-key-id",
    "aliyun.oss.access-key-secret=test-key-secret",
    "aliyun.oss.bucket-name=test-bucket",
    "aliyun.oss.video-dir=test/videos",
    "aliyun.oss.thumbnail-dir=test/thumbnails",
    "aliyun.oss.base-url=https://test-bucket.oss-cn-test.aliyuncs.com",

    // 数据库测试配置 - 使用root用户确保测试通过
    "spring.datasource.username=root",
    "spring.datasource.password=root",
    "spring.datasource.url=******************************************************************************************************************************************************"
})
public class OssConfigTest {

    @Autowired
    private OssConfig ossConfig;

    @Test
    public void testOssConfigProperties() {
        assertNotNull(ossConfig);
        assertEquals("https://oss-cn-test.aliyuncs.com", ossConfig.getEndpoint());
        assertEquals("test-key-id", ossConfig.getAccessKeyId());
        assertEquals("test-key-secret", ossConfig.getAccessKeySecret());
        assertEquals("test-bucket", ossConfig.getBucketName());
        assertEquals("test/videos", ossConfig.getVideoDir());
        assertEquals("test/thumbnails", ossConfig.getThumbnailDir());
        assertEquals("https://test-bucket.oss-cn-test.aliyuncs.com", ossConfig.getBaseUrl());
    }
}
